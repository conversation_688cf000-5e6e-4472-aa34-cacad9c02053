#!/usr/bin/env python3
"""
Test script to verify that all 404 error fixes are working correctly.
This script tests the URLs that were previously returning 404 errors.

This script should be run from the Superset root directory.
"""

import os
import json as json_module

def test_static_files():
    """Test that all static files exist"""
    print("Testing static files existence...")
    
    # Test icon files
    icon_files = [
        "superset/static/assets/images/favicon.ico",
        "superset/static/assets/images/apple-touch-icon.png", 
        "superset/static/assets/images/apple-touch-icon-precomposed.png",
        "superset/static/assets/images/favicon.png"  # Original file
    ]
    
    for file_path in icon_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing")
    
    # Test Font Awesome webfonts
    webfont_files = [
        "superset/static/appbuilder/webfonts/fa-solid-900.woff",
        "superset/static/appbuilder/webfonts/fa-solid-900.ttf",
        "superset/static/appbuilder/webfonts/fa-regular-400.woff2",
        "superset/static/appbuilder/webfonts/fa-regular-400.ttf"
    ]
    
    for file_path in webfont_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing")

def test_flask_routes():
    """Test that Flask routes are properly defined"""
    print("\nTesting Flask routes in superset/views/core.py...")
    
    try:
        with open("superset/views/core.py", "r") as f:
            content = f.read()
            
        # Check for robots.txt route
        if '@expose("/robots.txt")' in content and 'def robots_txt(' in content:
            print("✓ robots.txt route defined")
        else:
            print("✗ robots.txt route missing")
            
        # Check for favicon route
        if '@expose("/favicon.ico")' in content and 'def favicon(' in content:
            print("✓ favicon.ico route defined")
        else:
            print("✗ favicon.ico route missing")
            
        # Check for apple-touch-icon routes
        if ('@expose("/apple-touch-icon.png")' in content and 
            '@expose("/apple-touch-icon-precomposed.png")' in content and
            'def apple_touch_icon(' in content):
            print("✓ apple-touch-icon routes defined")
        else:
            print("✗ apple-touch-icon routes missing")
            
    except FileNotFoundError:
        print("✗ superset/views/core.py not found")

def test_manifest_json():
    """Test that manifest.json has proper theme entry"""
    print("\nTesting manifest.json...")
    
    try:
        with open("superset/static/assets/manifest.json", "r") as f:
            manifest = json_module.load(f)
            
        if "entrypoints" in manifest and "theme" in manifest["entrypoints"]:
            theme_entry = manifest["entrypoints"]["theme"]
            if "js" in theme_entry and len(theme_entry["js"]) > 0:
                theme_js = theme_entry["js"][0]
                print(f"✓ Theme JS entry found: {theme_js}")
                
                # Check if it's not the problematic [object Object] pattern
                if "[object Object]" not in theme_js:
                    print("✓ Theme JS entry looks correct (no [object Object])")
                else:
                    print("✗ Theme JS entry contains [object Object] - this needs fixing")
            else:
                print("✗ No theme JS entry found")
        else:
            print("✗ No theme entrypoint found in manifest")
            
    except FileNotFoundError:
        print("✗ manifest.json not found")
    except json_module.JSONDecodeError:
        print("✗ manifest.json is not valid JSON")

def main():
    """Main test function"""
    print("=== Testing 404 Error Fixes ===\n")

    # Change to Superset root directory (two levels up from cli)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    superset_root = os.path.dirname(os.path.dirname(script_dir))
    os.chdir(superset_root)
    print(f"Working directory: {os.getcwd()}\n")
    
    test_static_files()
    test_flask_routes()
    test_manifest_json()
    
    print("\n=== Test Summary ===")
    print("All tests completed. Check the output above for any issues.")
    print("\nTo fully test these fixes, you would need to:")
    print("1. Start the Superset server")
    print("2. Visit the URLs that were previously returning 404:")
    print("   - https://analytics.dodois.io/robots.txt")
    print("   - https://analytics.dodois.io/favicon.ico") 
    print("   - https://analytics.dodois.io/apple-touch-icon.png")
    print("   - https://analytics.dodois.io/apple-touch-icon-precomposed.png")
    print("   - https://analytics.dodois.io/static/appbuilder/webfonts/fa-solid-900.woff")
    print("   - https://analytics.dodois.io/static/appbuilder/webfonts/fa-solid-900.ttf")
    print("   - https://analytics.dodois.io/static/appbuilder/webfonts/fa-regular-400.woff2")
    print("   - https://analytics.dodois.io/static/appbuilder/webfonts/fa-regular-400.ttf")

if __name__ == "__main__":
    main()
